<template>
  <el-dialog
    v-model="visible"
    :title="`${teacher?.teacherName || '教师'} 的时间安排`"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="teacher-schedule-dialog">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="scheduleData" class="schedule-content">
        <!-- 教师基本信息 -->
        <div class="teacher-info">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="教师姓名">{{ teacher.teacherName }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ teacher.teacherPhone }}</el-descriptions-item>
            <el-descriptions-item label="教学组">{{ teacher.groupName || '未分组' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 时间表更新信息 -->
          <div v-if="lastUpdateInfo" class="update-time-section">
            <el-text size="small" type="info">
              可上课时间最后更新：{{ formatUpdateTime(lastUpdateInfo.lastUpdateTime) }}
              <span v-if="lastUpdateInfo.daysSinceLastUpdate !== null">
                （{{ lastUpdateInfo.daysSinceLastUpdate }}天前）
              </span>
            </el-text>
          </div>
        </div>

        <!-- 时间安排表格 -->
        <div class="schedule-table">
          <!-- 标题行：包含时间范围、图例和操作按钮 -->
          <div class="schedule-header-row">
            <h4>{{ scheduleData.startDate }} 至 {{ scheduleData.endDate }} 时间安排</h4>

            <div class="header-right">
              <!-- 图例 -->
              <div class="legend-inline">
                <span class="legend-label">图例：</span>
                <div class="legend-items">
                  <div class="legend-item">
                    <div class="legend-color status-available"></div>
                    <span>可排课</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color status-occupied"></div>
                    <span>已占用</span>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <el-button type="primary" @click="handleSelectTeacher" size="small">
                <el-icon><Check /></el-icon>
                选择此教师
              </el-button>
            </div>
          </div>

          <!-- 时间表 -->
          <div class="week-schedule">
            <!-- 表头 -->
            <div class="schedule-header">
              <div class="time-column">时间</div>
              <div class="days-header">
                <div
                  v-for="day in orderedDailySchedules"
                  :key="day.date"
                  class="day-column"
                >
                  <div class="day-name">{{ getWeekDayName(day.weekday) }}</div>
                  <div class="day-date">{{ formatDate(day.date) }}</div>
                </div>
              </div>
            </div>

            <!-- 时间网格 -->
            <div class="schedule-body">
              <!-- 时间轴背景 -->
              <div class="time-axis">
                <div
                  v-for="hour in displayHours"
                  :key="hour"
                  class="time-row"
                >
                  <div class="time-label">{{ formatHour(hour) }}</div>
                  <div class="time-line"></div>
                </div>
              </div>

              <!-- 每日时间段 -->
              <div class="days-container">
                <div
                  v-for="day in orderedDailySchedules"
                  :key="day.date"
                  class="day-column"
                >
                  <!-- 显示该天的所有时间段（不合并连续时间段） -->
                  <div
                    v-for="slot in day.timeSlots"
                    :key="`${slot.startTime}-${slot.endTime}-${slot.status}-${slot.id || Math.random()}`"
                    class="time-slot"
                    :class="`status-${slot.status}`"
                    :style="getAbsoluteSlotStyle(slot)"
                  >
                    <div class="slot-content">
                      <span class="slot-time">{{ slot.startTime }}-{{ slot.endTime }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { getTeacherWeeklyScheduleApi } from '@/api/management/teacherMatch'
import { checkTeacherTimeSlotUpdateTimeApi } from '@/api/management/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  },
  startDate: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'select'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const scheduleData = ref(null)
const lastUpdateInfo = ref(null)

// 常量
const weekDays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'] // 索引1-7对应周一到周日，但显示顺序需要调整
const displayHours = Array.from({ length: 18 }, (_, i) => i + 6) // 6-23点

// 计算属性 - 重新排序为周日开始的顺序
const orderedDailySchedules = computed(() => {
  const schedules = scheduleData.value?.dailySchedules || []
  // 按照周日(7)、周一(1)到周六(6)的顺序排列
//   return [...schedules].sort((a, b) => {
//     const aOrder = a.weekday === 7 ? 0 : a.weekday
//     const bOrder = b.weekday === 7 ? 0 : b.weekday
//     return aOrder - bOrder
//   })
  return [...schedules]
})



// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue && props.teacher && props.startDate) {
    loadTeacherSchedule()
  }
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
async function loadTeacherSchedule() {
  if (!props.teacher || !props.startDate) return

  loading.value = true
  try {
    const { data } = await getTeacherWeeklyScheduleApi(props.teacher.teacherId, props.startDate)
    scheduleData.value = data

    // 获取更新时间信息
    await fetchUpdateTimeInfo()
  } catch (error) {
    console.error('加载教师时间安排失败:', error)
    ElMessage.error('加载教师时间安排失败')
  } finally {
    loading.value = false
  }
}

// 获取时间表更新时间信息
async function fetchUpdateTimeInfo() {
  if (!props.teacher?.teacherId) return

  try {
    const response = await checkTeacherTimeSlotUpdateTimeApi(props.teacher.teacherId)
    if (response.code === 200) {
      lastUpdateInfo.value = response.data
    }
  } catch (error) {
    console.error('获取时间表更新时间失败:', error)
    // 不显示错误信息，静默失败
  }
}

function getWeekDayName(weekday) {
  return weekDays[weekday] || '未知'
}

function formatDate(dateStr) {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`
}

// 格式化更新时间
function formatUpdateTime(updateTime) {
  if (!updateTime) return '未知'

  try {
    const date = new Date(updateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}



// 计算时间段的绝对定位样式
function getAbsoluteSlotStyle(slot) {
  const startTime = slot.startTime.split(':')
  const endTime = slot.endTime.split(':')
  const startHour = parseInt(startTime[0])
  const startMinute = parseInt(startTime[1])
  const endHour = parseInt(endTime[0])
  const endMinute = parseInt(endTime[1])

  // 计算开始时间相对于6:00的分钟数
  const startMinutesFromSix = (startHour - 6) * 60 + startMinute
  // 计算结束时间相对于6:00的分钟数
  const endMinutesFromSix = (endHour - 6) * 60 + endMinute

  // 计算持续时间（分钟）
  const durationMinutes = endMinutesFromSix - startMinutesFromSix

  // 每小时40px，每分钟40/60px
  const pixelsPerMinute = 40 / 60
  const top = startMinutesFromSix * pixelsPerMinute
  const height = durationMinutes * pixelsPerMinute

  return {
    position: 'absolute',
    top: `${top}px`,
    height: `${height}px`,
    left: '2px',
    right: '2px',
    zIndex: 1
  }
}



function handleSelectTeacher() {
  emit('select', props.teacher)
  visible.value = false
}

function handleClose() {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.teacher-schedule-dialog {
  .loading-container {
    padding: 20px;
  }

  .schedule-content {
    .teacher-info {
      margin-bottom: 20px;

      .update-time-section {
        margin-top: 12px;
        padding: 8px 12px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #409eff;
      }
    }

    .schedule-table {
      margin-bottom: 20px;

      .schedule-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h4 {
          margin: 0;
          color: #303133;
          flex-shrink: 0;
        }

        .header-right {
          display: flex;
          align-items: center;
          gap: 16px;

          .legend-inline {
            display: flex;
            align-items: center;
            gap: 8px;

            .legend-label {
              font-size: 12px;
              color: #606266;
              font-weight: 500;
            }

            .legend-items {
              display: flex;
              gap: 12px;

              .legend-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #606266;

                .legend-color {
                  width: 12px;
                  height: 12px;
                  border-radius: 2px;
                  flex-shrink: 0;

                  &.status-available {
                    background-color: #e1f3d8;
                    border: 1px solid #67c23a;
                  }

                  &.status-occupied {
                    background-color: #fdf6ec;
                    border: 1px solid #e6a23c;
                  }
                }
              }
            }
          }
        }
      }

      .week-schedule {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        overflow: hidden;

        .schedule-header {
          display: flex;
          background-color: #f8f9fa;
          border-bottom: 1px solid #e4e7ed;

          .time-column {
            width: 80px;
            padding: 12px 8px;
            border-right: 1px solid #e4e7ed;
            font-weight: 600;
            text-align: center;
            flex-shrink: 0;
            box-sizing: border-box;
          }

          .days-header {
            flex: 1;
            display: flex;

            .day-column {
              flex: 1;
              padding: 8px;
              text-align: center;
              border-right: 1px solid #e4e7ed;
              box-sizing: border-box;

              &:last-child {
                border-right: none;
              }

              .day-name {
                font-weight: 600;
                color: #303133;
                margin-bottom: 2px;
              }

              .day-date {
                font-size: 11px;
                color: #909399;
              }
            }
          }
        }

        .schedule-body {
          position: relative;
          height: 720px; // 18小时 * 40px

          .time-axis {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;

            .time-row {
              display: flex;
              border-bottom: 1px solid #f0f0f0;
              height: 40px;

              &:last-child {
                border-bottom: none;
              }

              .time-label {
                width: 80px;
                padding: 8px;
                border-right: 1px solid #e4e7ed;
                font-size: 12px;
                color: #909399;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fafbfc;
                flex-shrink: 0;
                box-sizing: border-box;
              }

              .time-line {
                flex: 1;
                border-right: 1px solid #f0f0f0;
              }
            }
          }

          .days-container {
            position: absolute;
            left: 80px; // 从时间列后开始
            top: 0;
            right: 0;
            height: 100%;
            display: flex; // 使用flex布局确保等宽

            .day-column {
              flex: 1; // 每列平均分配宽度
              height: 100%;
              border-right: 1px solid #f0f0f0;
              position: relative;
              box-sizing: border-box;

              &:last-child {
                border-right: none;
              }

              .time-slot {
                border-radius: 3px;
                font-size: 10px;
                min-height: 20px;

                &.status-available {
                  background-color: #e1f3d8;
                  border: 1px solid #67c23a;
                  color: #67c23a;
                }

                &.status-occupied {
                  background-color: #fdf6ec;
                  border: 1px solid #e6a23c;
                  color: #e6a23c;
                }

                .slot-content {
                  padding: 2px 4px;
                  text-align: center;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .slot-time {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 10px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
